/**
 * 用户认证状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'
import { useAppStore } from './app'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref(null)
  const token = ref(localStorage.getItem('access_token'))
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => {
    if (!user.value) return false
    return user.value.is_superuser || 
           user.value.roles?.some(role => ['admin', 'super_admin'].includes(role.name))
  })
  const isSuperUser = computed(() => user.value?.is_superuser || false)

  // 获取用户权限列表
  const userPermissions = computed(() => {
    if (!user.value?.roles) return []
    const permissions = new Set()
    user.value.roles.forEach(role => {
      role.permissions?.forEach(permission => {
        permissions.add(permission.name)
      })
    })
    return Array.from(permissions)
  })

  // 检查用户是否具有指定权限
  const hasPermission = (permission) => {
    if (isSuperUser.value) return true
    return userPermissions.value.includes(permission)
  }

  // 检查用户是否具有资源操作权限
  const hasResourcePermission = (resource, action) => {
    return hasPermission(`${resource}:${action}`)
  }

  // 操作
  const setToken = (newToken) => {
    token.value = newToken
    if (newToken) {
      localStorage.setItem('access_token', newToken)
      // 设置 axios 默认 header
      axios.defaults.headers.common['Authorization'] = `Bearer ${newToken}`
    } else {
      localStorage.removeItem('access_token')
      delete axios.defaults.headers.common['Authorization']
    }
  }

  const setUser = (userData) => {
    user.value = userData
  }

  const login = async (credentials) => {
    const appStore = useAppStore()
    isLoading.value = true

    try {
      const response = await axios.post('/api/v1/auth/login', credentials)
      const { access_token, user: userData } = response.data

      setToken(access_token)
      setUser(userData)

      appStore.showSuccess('登录成功', `欢迎回来，${userData.full_name || userData.username}！`)
      
      return { success: true, user: userData }
    } catch (error) {
      const message = error.response?.data?.detail || '登录失败'
      appStore.showError('登录失败', message)
      return { success: false, error: message }
    } finally {
      isLoading.value = false
    }
  }

  const register = async (userData) => {
    const appStore = useAppStore()
    isLoading.value = true

    try {
      const response = await axios.post('/api/v1/auth/register', userData)
      const newUser = response.data

      appStore.showSuccess('注册成功', '账户创建成功，请登录')
      
      return { success: true, user: newUser }
    } catch (error) {
      const message = error.response?.data?.detail || '注册失败'
      appStore.showError('注册失败', message)
      return { success: false, error: message }
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    const appStore = useAppStore()
    isLoading.value = true

    try {
      // 调用后端登出接口
      if (token.value) {
        await axios.post('/api/v1/auth/logout')
      }
    } catch (error) {
      console.warn('登出请求失败:', error)
    } finally {
      // 清除本地状态
      setToken(null)
      setUser(null)
      isLoading.value = false
      
      appStore.showInfo('已登出', '您已成功登出')
    }
  }

  const fetchCurrentUser = async () => {
    if (!token.value) return

    try {
      const response = await axios.get('/api/v1/auth/me')
      setUser(response.data)
      return response.data
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果token无效，清除认证状态
      if (error.response?.status === 401) {
        setToken(null)
        setUser(null)
      }
      throw error
    }
  }

  const updateProfile = async (profileData) => {
    const appStore = useAppStore()
    isLoading.value = true

    try {
      const response = await axios.put('/api/v1/auth/me', profileData)
      setUser(response.data)
      
      appStore.showSuccess('更新成功', '个人信息已更新')
      return { success: true, user: response.data }
    } catch (error) {
      const message = error.response?.data?.detail || '更新失败'
      appStore.showError('更新失败', message)
      return { success: false, error: message }
    } finally {
      isLoading.value = false
    }
  }

  const changePassword = async (passwordData) => {
    const appStore = useAppStore()
    isLoading.value = true

    try {
      await axios.put('/api/v1/auth/me/password', passwordData)
      
      appStore.showSuccess('密码修改成功', '请使用新密码重新登录')
      
      // 修改密码后自动登出
      await logout()
      
      return { success: true }
    } catch (error) {
      const message = error.response?.data?.detail || '密码修改失败'
      appStore.showError('密码修改失败', message)
      return { success: false, error: message }
    } finally {
      isLoading.value = false
    }
  }

  const requestPasswordReset = async (email) => {
    const appStore = useAppStore()
    isLoading.value = true

    try {
      await axios.post('/api/v1/auth/password-reset/request', { email })
      
      appStore.showSuccess('重置邮件已发送', '请检查您的邮箱')
      return { success: true }
    } catch (error) {
      const message = error.response?.data?.detail || '请求失败'
      appStore.showError('请求失败', message)
      return { success: false, error: message }
    } finally {
      isLoading.value = false
    }
  }

  const resetPassword = async (resetData) => {
    const appStore = useAppStore()
    isLoading.value = true

    try {
      await axios.post('/api/v1/auth/password-reset/confirm', resetData)
      
      appStore.showSuccess('密码重置成功', '请使用新密码登录')
      return { success: true }
    } catch (error) {
      const message = error.response?.data?.detail || '密码重置失败'
      appStore.showError('密码重置失败', message)
      return { success: false, error: message }
    } finally {
      isLoading.value = false
    }
  }

  const verifyToken = async () => {
    if (!token.value) return false

    try {
      const response = await axios.get('/api/v1/auth/verify-token')
      return response.data.valid
    } catch (error) {
      console.error('Token验证失败:', error)
      return false
    }
  }

  // 初始化认证状态
  const initAuth = async () => {
    if (token.value) {
      // 设置 axios 默认 header
      axios.defaults.headers.common['Authorization'] = `Bearer ${token.value}`
      
      try {
        // 验证token并获取用户信息
        const isValid = await verifyToken()
        if (isValid) {
          await fetchCurrentUser()
        } else {
          // token无效，清除状态
          setToken(null)
          setUser(null)
        }
      } catch (error) {
        console.error('初始化认证状态失败:', error)
        setToken(null)
        setUser(null)
      }
    }
  }

  return {
    // 状态
    user,
    token,
    isLoading,
    
    // 计算属性
    isAuthenticated,
    isAdmin,
    isSuperUser,
    userPermissions,
    
    // 方法
    hasPermission,
    hasResourcePermission,
    login,
    register,
    logout,
    fetchCurrentUser,
    updateProfile,
    changePassword,
    requestPasswordReset,
    resetPassword,
    verifyToken,
    initAuth
  }
})
