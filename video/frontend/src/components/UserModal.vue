<template>
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- 背景遮罩 -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="close"></div>

      <!-- 模态框 -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <form @submit.prevent="handleSubmit">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                  {{ isEdit ? '编辑用户' : '创建用户' }}
                </h3>
                
                <div class="space-y-4">
                  <div>
                    <label for="username" class="block text-sm font-medium text-gray-700">
                      用户名 *
                    </label>
                    <input
                      id="username"
                      v-model="form.username"
                      type="text"
                      required
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                      placeholder="请输入用户名"
                    />
                  </div>

                  <div>
                    <label for="email" class="block text-sm font-medium text-gray-700">
                      邮箱地址 *
                    </label>
                    <input
                      id="email"
                      v-model="form.email"
                      type="email"
                      required
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                      placeholder="请输入邮箱地址"
                    />
                  </div>

                  <div>
                    <label for="full_name" class="block text-sm font-medium text-gray-700">
                      姓名
                    </label>
                    <input
                      id="full_name"
                      v-model="form.full_name"
                      type="text"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                      placeholder="请输入姓名"
                    />
                  </div>

                  <div v-if="!isEdit">
                    <label for="password" class="block text-sm font-medium text-gray-700">
                      密码 *
                    </label>
                    <input
                      id="password"
                      v-model="form.password"
                      type="password"
                      required
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                      placeholder="请输入密码"
                    />
                  </div>

                  <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                      <input
                        id="is_active"
                        v-model="form.is_active"
                        type="checkbox"
                        class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                      <label for="is_active" class="ml-2 block text-sm text-gray-900">
                        激活状态
                      </label>
                    </div>

                    <div class="flex items-center">
                      <input
                        id="is_superuser"
                        v-model="form.is_superuser"
                        type="checkbox"
                        class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                      <label for="is_superuser" class="ml-2 block text-sm text-gray-900">
                        超级用户
                      </label>
                    </div>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      角色
                    </label>
                    <div class="space-y-2 max-h-32 overflow-y-auto border border-gray-300 rounded-md p-2">
                      <div
                        v-for="role in availableRoles"
                        :key="role.id"
                        class="flex items-center"
                      >
                        <input
                          :id="`role-${role.id}`"
                          v-model="form.role_ids"
                          :value="role.id"
                          type="checkbox"
                          class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        />
                        <label :for="`role-${role.id}`" class="ml-2 block text-sm text-gray-900">
                          {{ role.name }} - {{ role.description }}
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="submit"
              :disabled="isLoading"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
            >
              <span v-if="isLoading" class="flex items-center">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {{ isEdit ? '更新中...' : '创建中...' }}
              </span>
              <span v-else>{{ isEdit ? '更新' : '创建' }}</span>
            </button>
            <button
              type="button"
              @click="close"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              取消
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import axios from 'axios'
import { useAppStore } from '@/stores/app'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  user: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close', 'success'])

const appStore = useAppStore()

// 状态
const isLoading = ref(false)
const availableRoles = ref([])

// 表单数据
const form = ref({
  username: '',
  email: '',
  full_name: '',
  password: '',
  is_active: true,
  is_superuser: false,
  role_ids: []
})

// 计算属性
const isEdit = computed(() => !!props.user)

// 方法
const resetForm = () => {
  form.value = {
    username: '',
    email: '',
    full_name: '',
    password: '',
    is_active: true,
    is_superuser: false,
    role_ids: []
  }
}

const loadRoles = async () => {
  try {
    const response = await axios.get('/api/v1/roles/')
    availableRoles.value = response.data
  } catch (error) {
    console.error('加载角色失败:', error)
  }
}

const handleSubmit = async () => {
  isLoading.value = true
  
  try {
    let response
    if (isEdit.value) {
      // 更新用户
      response = await axios.put(`/api/v1/users/${props.user.id}`, form.value)
    } else {
      // 创建用户
      response = await axios.post('/api/v1/users/', form.value)
    }
    
    appStore.showSuccess(
      isEdit.value ? '更新成功' : '创建成功',
      `用户 ${form.value.username} ${isEdit.value ? '已更新' : '已创建'}`
    )
    
    emit('success', response.data)
    close()
  } catch (error) {
    const message = error.response?.data?.detail || `${isEdit.value ? '更新' : '创建'}用户失败`
    appStore.showError(`${isEdit.value ? '更新' : '创建'}失败`, message)
  } finally {
    isLoading.value = false
  }
}

const close = () => {
  resetForm()
  emit('close')
}

// 监听器
watch(() => props.show, (newVal) => {
  if (newVal) {
    loadRoles()
    if (props.user) {
      // 编辑模式，填充表单
      form.value = {
        username: props.user.username || '',
        email: props.user.email || '',
        full_name: props.user.full_name || '',
        password: '', // 编辑时不显示密码
        is_active: props.user.is_active ?? true,
        is_superuser: props.user.is_superuser ?? false,
        role_ids: props.user.roles?.map(role => role.id) || []
      }
    } else {
      resetForm()
    }
  }
})

// 生命周期
onMounted(() => {
  loadRoles()
})
</script>
