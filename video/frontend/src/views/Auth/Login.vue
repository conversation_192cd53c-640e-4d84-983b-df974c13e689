<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100">
          <VideoCameraIcon class="h-8 w-8 text-primary-600" />
        </div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          登录您的账户
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          或者
          <router-link 
            to="/register" 
            class="font-medium text-primary-600 hover:text-primary-500"
          >
            创建新账户
          </router-link>
        </p>
      </div>
      
      <form class="mt-8 space-y-6" @submit.prevent="handleLogin">
        <div class="rounded-md shadow-sm -space-y-px">
          <div>
            <label for="username" class="sr-only">用户名或邮箱</label>
            <input
              id="username"
              v-model="form.username"
              name="username"
              type="text"
              autocomplete="username"
              required
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
              placeholder="用户名或邮箱"
            />
          </div>
          <div>
            <label for="password" class="sr-only">密码</label>
            <input
              id="password"
              v-model="form.password"
              name="password"
              type="password"
              autocomplete="current-password"
              required
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
              placeholder="密码"
            />
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <input
              id="remember-me"
              v-model="form.remember_me"
              name="remember-me"
              type="checkbox"
              class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label for="remember-me" class="ml-2 block text-sm text-gray-900">
              记住登录状态
            </label>
          </div>

          <div class="text-sm">
            <router-link 
              to="/forgot-password" 
              class="font-medium text-primary-600 hover:text-primary-500"
            >
              忘记密码？
            </router-link>
          </div>
        </div>

        <div>
          <button
            type="submit"
            :disabled="authStore.isLoading"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
              <LockClosedIcon 
                class="h-5 w-5 text-primary-500 group-hover:text-primary-400" 
                aria-hidden="true" 
              />
            </span>
            <span v-if="authStore.isLoading" class="flex items-center">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              登录中...
            </span>
            <span v-else>登录</span>
          </button>
        </div>

        <!-- 开发环境快速登录 -->
        <div v-if="isDev" class="mt-6 border-t border-gray-200 pt-6">
          <p class="text-center text-sm text-gray-500 mb-4">开发环境快速登录</p>
          <div class="grid grid-cols-2 gap-3">
            <button
              type="button"
              @click="quickLogin('admin')"
              class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
            >
              管理员
            </button>
            <button
              type="button"
              @click="quickLogin('user')"
              class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
            >
              普通用户
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { VideoCameraIcon, LockClosedIcon } from '@heroicons/vue/24/outline'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 表单数据
const form = ref({
  username: '',
  password: '',
  remember_me: false
})

// 是否为开发环境
const isDev = computed(() => import.meta.env.DEV)

// 处理登录
const handleLogin = async () => {
  const result = await authStore.login(form.value)
  
  if (result.success) {
    // 登录成功，重定向到首页或之前的页面
    const redirect = router.currentRoute.value.query.redirect || '/'
    router.push(redirect)
  }
}

// 快速登录（开发环境）
const quickLogin = async (type) => {
  const credentials = {
    admin: { username: 'admin', password: 'admin123456', remember_me: false },
    user: { username: 'testuser', password: 'test123456', remember_me: false }
  }
  
  if (credentials[type]) {
    form.value = { ...credentials[type] }
    await handleLogin()
  }
}
</script>

<style scoped>
/* 自定义样式 */
.bg-primary-100 {
  background-color: rgb(239 246 255);
}

.text-primary-600 {
  color: rgb(37 99 235);
}

.text-primary-500 {
  color: rgb(59 130 246);
}

.bg-primary-600 {
  background-color: rgb(37 99 235);
}

.bg-primary-700 {
  background-color: rgb(29 78 216);
}

.border-primary-500 {
  border-color: rgb(59 130 246);
}

.ring-primary-500 {
  --tw-ring-color: rgb(59 130 246);
}

.focus\:ring-primary-500:focus {
  --tw-ring-color: rgb(59 130 246);
}

.focus\:border-primary-500:focus {
  border-color: rgb(59 130 246);
}

.hover\:bg-primary-700:hover {
  background-color: rgb(29 78 216);
}

.hover\:text-primary-500:hover {
  color: rgb(59 130 246);
}
</style>
