<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100">
          <KeyIcon class="h-8 w-8 text-primary-600" />
        </div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          重置密码
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          输入您的邮箱地址，我们将发送重置链接给您
        </p>
      </div>
      
      <form class="mt-8 space-y-6" @submit.prevent="handleSubmit">
        <div>
          <label for="email" class="sr-only">邮箱地址</label>
          <input
            id="email"
            v-model="email"
            name="email"
            type="email"
            autocomplete="email"
            required
            class="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
            placeholder="请输入邮箱地址"
          />
        </div>

        <div>
          <button
            type="submit"
            :disabled="authStore.isLoading || !email"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
              <KeyIcon 
                class="h-5 w-5 text-primary-500 group-hover:text-primary-400" 
                aria-hidden="true" 
              />
            </span>
            <span v-if="authStore.isLoading" class="flex items-center">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              发送中...
            </span>
            <span v-else>发送重置链接</span>
          </button>
        </div>

        <div class="text-center">
          <router-link 
            to="/login" 
            class="font-medium text-primary-600 hover:text-primary-500"
          >
            返回登录
          </router-link>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { KeyIcon } from '@heroicons/vue/24/outline'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const email = ref('')

const handleSubmit = async () => {
  const result = await authStore.requestPasswordReset(email.value)
  
  if (result.success) {
    // 重置成功，跳转到登录页面
    router.push('/login')
  }
}
</script>

<style scoped>
/* 使用与登录页面相同的样式 */
.bg-primary-100 {
  background-color: rgb(239 246 255);
}

.text-primary-600 {
  color: rgb(37 99 235);
}

.text-primary-500 {
  color: rgb(59 130 246);
}

.bg-primary-600 {
  background-color: rgb(37 99 235);
}

.bg-primary-700 {
  background-color: rgb(29 78 216);
}

.border-primary-500 {
  border-color: rgb(59 130 246);
}

.ring-primary-500 {
  --tw-ring-color: rgb(59 130 246);
}

.focus\:ring-primary-500:focus {
  --tw-ring-color: rgb(59 130 246);
}

.focus\:border-primary-500:focus {
  border-color: rgb(59 130 246);
}

.hover\:bg-primary-700:hover {
  background-color: rgb(29 78 216);
}

.hover\:text-primary-500:hover {
  color: rgb(59 130 246);
}
</style>
