<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100">
          <UserPlusIcon class="h-8 w-8 text-primary-600" />
        </div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          创建新账户
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          或者
          <router-link 
            to="/login" 
            class="font-medium text-primary-600 hover:text-primary-500"
          >
            登录现有账户
          </router-link>
        </p>
      </div>
      
      <form class="mt-8 space-y-6" @submit.prevent="handleRegister">
        <div class="space-y-4">
          <div>
            <label for="username" class="block text-sm font-medium text-gray-700">
              用户名 *
            </label>
            <input
              id="username"
              v-model="form.username"
              name="username"
              type="text"
              autocomplete="username"
              required
              class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              placeholder="请输入用户名"
            />
            <p class="mt-1 text-xs text-gray-500">3-50个字符，支持字母、数字和下划线</p>
          </div>

          <div>
            <label for="email" class="block text-sm font-medium text-gray-700">
              邮箱地址 *
            </label>
            <input
              id="email"
              v-model="form.email"
              name="email"
              type="email"
              autocomplete="email"
              required
              class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              placeholder="请输入邮箱地址"
            />
          </div>

          <div>
            <label for="full_name" class="block text-sm font-medium text-gray-700">
              姓名
            </label>
            <input
              id="full_name"
              v-model="form.full_name"
              name="full_name"
              type="text"
              autocomplete="name"
              class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              placeholder="请输入您的姓名（可选）"
            />
          </div>

          <div>
            <label for="password" class="block text-sm font-medium text-gray-700">
              密码 *
            </label>
            <input
              id="password"
              v-model="form.password"
              name="password"
              type="password"
              autocomplete="new-password"
              required
              class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              placeholder="请输入密码"
            />
            <p class="mt-1 text-xs text-gray-500">至少6个字符</p>
          </div>

          <div>
            <label for="confirm_password" class="block text-sm font-medium text-gray-700">
              确认密码 *
            </label>
            <input
              id="confirm_password"
              v-model="form.confirm_password"
              name="confirm_password"
              type="password"
              autocomplete="new-password"
              required
              class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              placeholder="请再次输入密码"
              :class="{ 'border-red-300': passwordMismatch }"
            />
            <p v-if="passwordMismatch" class="mt-1 text-xs text-red-600">
              两次输入的密码不一致
            </p>
          </div>
        </div>

        <div class="flex items-center">
          <input
            id="agree-terms"
            v-model="form.agree_terms"
            name="agree-terms"
            type="checkbox"
            required
            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
          />
          <label for="agree-terms" class="ml-2 block text-sm text-gray-900">
            我同意
            <a href="#" class="text-primary-600 hover:text-primary-500">服务条款</a>
            和
            <a href="#" class="text-primary-600 hover:text-primary-500">隐私政策</a>
          </label>
        </div>

        <div>
          <button
            type="submit"
            :disabled="authStore.isLoading || !isFormValid"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
              <UserPlusIcon 
                class="h-5 w-5 text-primary-500 group-hover:text-primary-400" 
                aria-hidden="true" 
              />
            </span>
            <span v-if="authStore.isLoading" class="flex items-center">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              注册中...
            </span>
            <span v-else>创建账户</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { UserPlusIcon } from '@heroicons/vue/24/outline'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 表单数据
const form = ref({
  username: '',
  email: '',
  full_name: '',
  password: '',
  confirm_password: '',
  agree_terms: false
})

// 计算属性
const passwordMismatch = computed(() => {
  return form.value.password && form.value.confirm_password && 
         form.value.password !== form.value.confirm_password
})

const isFormValid = computed(() => {
  return form.value.username && 
         form.value.email && 
         form.value.password && 
         form.value.confirm_password &&
         form.value.password === form.value.confirm_password &&
         form.value.agree_terms
})

// 处理注册
const handleRegister = async () => {
  if (!isFormValid.value) {
    return
  }

  const registerData = {
    username: form.value.username,
    email: form.value.email,
    password: form.value.password,
    full_name: form.value.full_name || null
  }

  const result = await authStore.register(registerData)
  
  if (result.success) {
    // 注册成功，跳转到登录页面
    router.push('/login')
  }
}
</script>

<style scoped>
/* 使用与登录页面相同的样式 */
.bg-primary-100 {
  background-color: rgb(239 246 255);
}

.text-primary-600 {
  color: rgb(37 99 235);
}

.text-primary-500 {
  color: rgb(59 130 246);
}

.bg-primary-600 {
  background-color: rgb(37 99 235);
}

.bg-primary-700 {
  background-color: rgb(29 78 216);
}

.border-primary-500 {
  border-color: rgb(59 130 246);
}

.ring-primary-500 {
  --tw-ring-color: rgb(59 130 246);
}

.focus\:ring-primary-500:focus {
  --tw-ring-color: rgb(59 130 246);
}

.focus\:border-primary-500:focus {
  border-color: rgb(59 130 246);
}

.hover\:bg-primary-700:hover {
  background-color: rgb(29 78 216);
}

.hover\:text-primary-500:hover {
  color: rgb(59 130 246);
}
</style>
