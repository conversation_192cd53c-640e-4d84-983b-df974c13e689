<template>
  <div class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
    <div class="bg-white shadow rounded-lg">
      <!-- 头部 -->
      <div class="px-6 py-4 border-b border-gray-200">
        <h1 class="text-2xl font-bold text-gray-900">个人中心</h1>
        <p class="mt-1 text-sm text-gray-600">管理您的账户信息和设置</p>
      </div>

      <!-- 标签页 -->
      <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            @click="activeTab = tab.id"
            :class="[
              activeTab === tab.id
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
              'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm'
            ]"
          >
            <component :is="tab.icon" class="w-5 h-5 mr-2 inline" />
            {{ tab.name }}
          </button>
        </nav>
      </div>

      <!-- 内容区域 -->
      <div class="p-6">
        <!-- 基本信息 -->
        <div v-if="activeTab === 'profile'" class="space-y-6">
          <div class="flex items-center space-x-6">
            <div class="flex-shrink-0">
              <div class="h-20 w-20 rounded-full bg-gray-300 flex items-center justify-center">
                <UserIcon class="h-12 w-12 text-gray-600" />
              </div>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900">{{ authStore.user?.full_name || authStore.user?.username }}</h3>
              <p class="text-sm text-gray-500">{{ authStore.user?.email }}</p>
              <div class="mt-2 flex items-center space-x-2">
                <span 
                  :class="[
                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                    authStore.user?.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  ]"
                >
                  {{ authStore.user?.is_active ? '活跃' : '已停用' }}
                </span>
                <span 
                  v-if="authStore.isSuperUser"
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
                >
                  超级管理员
                </span>
                <span 
                  v-else-if="authStore.isAdmin"
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  管理员
                </span>
              </div>
            </div>
          </div>

          <!-- 编辑表单 -->
          <form @submit.prevent="updateProfile" class="space-y-4">
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label for="username" class="block text-sm font-medium text-gray-700">
                  用户名
                </label>
                <input
                  id="username"
                  v-model="profileForm.username"
                  type="text"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                />
              </div>

              <div>
                <label for="email" class="block text-sm font-medium text-gray-700">
                  邮箱地址
                </label>
                <input
                  id="email"
                  v-model="profileForm.email"
                  type="email"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                />
              </div>

              <div class="sm:col-span-2">
                <label for="full_name" class="block text-sm font-medium text-gray-700">
                  姓名
                </label>
                <input
                  id="full_name"
                  v-model="profileForm.full_name"
                  type="text"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                />
              </div>
            </div>

            <div class="flex justify-end">
              <button
                type="submit"
                :disabled="authStore.isLoading"
                class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
              >
                <span v-if="authStore.isLoading" class="flex items-center">
                  <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  更新中...
                </span>
                <span v-else>更新信息</span>
              </button>
            </div>
          </form>
        </div>

        <!-- 密码修改 -->
        <div v-if="activeTab === 'password'" class="space-y-6">
          <div>
            <h3 class="text-lg font-medium text-gray-900">修改密码</h3>
            <p class="mt-1 text-sm text-gray-600">为了账户安全，请定期更换密码</p>
          </div>

          <form @submit.prevent="changePassword" class="space-y-4 max-w-md">
            <div>
              <label for="current_password" class="block text-sm font-medium text-gray-700">
                当前密码
              </label>
              <input
                id="current_password"
                v-model="passwordForm.current_password"
                type="password"
                required
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            <div>
              <label for="new_password" class="block text-sm font-medium text-gray-700">
                新密码
              </label>
              <input
                id="new_password"
                v-model="passwordForm.new_password"
                type="password"
                required
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            <div>
              <label for="confirm_password" class="block text-sm font-medium text-gray-700">
                确认新密码
              </label>
              <input
                id="confirm_password"
                v-model="passwordForm.confirm_password"
                type="password"
                required
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                :class="{ 'border-red-300': passwordMismatch }"
              />
              <p v-if="passwordMismatch" class="mt-1 text-xs text-red-600">
                两次输入的密码不一致
              </p>
            </div>

            <div>
              <button
                type="submit"
                :disabled="authStore.isLoading || !isPasswordFormValid"
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
              >
                <span v-if="authStore.isLoading" class="flex items-center">
                  <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  修改中...
                </span>
                <span v-else>修改密码</span>
              </button>
            </div>
          </form>
        </div>

        <!-- 角色权限 -->
        <div v-if="activeTab === 'roles'" class="space-y-6">
          <div>
            <h3 class="text-lg font-medium text-gray-900">角色与权限</h3>
            <p class="mt-1 text-sm text-gray-600">您当前拥有的角色和权限</p>
          </div>

          <div class="space-y-4">
            <div>
              <h4 class="text-sm font-medium text-gray-900 mb-2">角色</h4>
              <div class="flex flex-wrap gap-2">
                <span
                  v-for="role in authStore.user?.roles || []"
                  :key="role.id"
                  class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                >
                  {{ role.name }}
                </span>
                <span v-if="!authStore.user?.roles?.length" class="text-sm text-gray-500">
                  暂无角色
                </span>
              </div>
            </div>

            <div>
              <h4 class="text-sm font-medium text-gray-900 mb-2">权限</h4>
              <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                <span
                  v-for="permission in authStore.userPermissions"
                  :key="permission"
                  class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800"
                >
                  {{ permission }}
                </span>
                <span v-if="!authStore.userPermissions.length" class="text-sm text-gray-500">
                  暂无权限
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { UserIcon, KeyIcon, ShieldCheckIcon } from '@heroicons/vue/24/outline'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

// 标签页
const tabs = [
  { id: 'profile', name: '基本信息', icon: UserIcon },
  { id: 'password', name: '密码修改', icon: KeyIcon },
  { id: 'roles', name: '角色权限', icon: ShieldCheckIcon }
]

const activeTab = ref('profile')

// 个人信息表单
const profileForm = ref({
  username: '',
  email: '',
  full_name: ''
})

// 密码修改表单
const passwordForm = ref({
  current_password: '',
  new_password: '',
  confirm_password: ''
})

// 计算属性
const passwordMismatch = computed(() => {
  return passwordForm.value.new_password && passwordForm.value.confirm_password && 
         passwordForm.value.new_password !== passwordForm.value.confirm_password
})

const isPasswordFormValid = computed(() => {
  return passwordForm.value.current_password && 
         passwordForm.value.new_password && 
         passwordForm.value.confirm_password &&
         passwordForm.value.new_password === passwordForm.value.confirm_password
})

// 方法
const updateProfile = async () => {
  await authStore.updateProfile(profileForm.value)
}

const changePassword = async () => {
  if (!isPasswordFormValid.value) return

  const result = await authStore.changePassword({
    current_password: passwordForm.value.current_password,
    new_password: passwordForm.value.new_password
  })

  if (result.success) {
    // 清空表单
    passwordForm.value = {
      current_password: '',
      new_password: '',
      confirm_password: ''
    }
  }
}

// 初始化
onMounted(() => {
  if (authStore.user) {
    profileForm.value = {
      username: authStore.user.username || '',
      email: authStore.user.email || '',
      full_name: authStore.user.full_name || ''
    }
  }
})
</script>
