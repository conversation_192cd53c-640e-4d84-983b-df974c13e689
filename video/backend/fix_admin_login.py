#!/usr/bin/env python3
"""
修复管理员登录问题
使用与系统相同的密码哈希方法
"""

import sys
import os
import sqlite3

# 添加app目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def fix_admin_login():
    """修复管理员登录"""
    try:
        # 导入AuthService
        from app.core.auth import AuthService
        print("✅ 成功导入AuthService")
        
        # 连接数据库
        db_path = './app.db'
        if not os.path.exists(db_path):
            print(f"❌ 数据库文件不存在: {db_path}")
            return False
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        print("✅ 连接数据库成功")
        
        # 检查用户表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        if not cursor.fetchone():
            print("❌ 用户表不存在，需要先初始化数据库")
            return False
        
        # 查看现有admin用户
        cursor.execute("SELECT id, username, email, hashed_password, is_active, is_superuser FROM users WHERE username='admin'")
        admin_user = cursor.fetchone()
        
        if admin_user:
            print(f"✅ 找到管理员用户:")
            print(f"   ID: {admin_user[0]}")
            print(f"   用户名: {admin_user[1]}")
            print(f"   邮箱: {admin_user[2]}")
            print(f"   当前密码哈希: {admin_user[3][:50]}...")
            print(f"   激活状态: {admin_user[4]}")
            print(f"   超级用户: {admin_user[5]}")
        else:
            print("❌ 未找到管理员用户，将创建新用户")
        
        # 设置新密码
        new_password = "admin123456"
        print(f"🔑 设置密码为: {new_password}")
        
        # 使用AuthService生成密码哈希
        hashed_password = AuthService.get_password_hash(new_password)
        print(f"🔐 生成密码哈希: {hashed_password[:50]}...")
        
        if admin_user:
            # 更新现有用户
            cursor.execute("""
                UPDATE users 
                SET hashed_password = ?, is_active = 1, is_superuser = 1
                WHERE username = 'admin'
            """, (hashed_password,))
            print("✅ 更新现有管理员用户")
        else:
            # 创建新用户
            cursor.execute("""
                INSERT INTO users (username, email, full_name, hashed_password, is_active, is_superuser)
                VALUES ('admin', '<EMAIL>', '系统管理员', ?, 1, 1)
            """, (hashed_password,))
            print("✅ 创建新管理员用户")
        
        conn.commit()
        
        # 验证更新结果
        cursor.execute("SELECT id, username, email, hashed_password, is_active, is_superuser FROM users WHERE username='admin'")
        updated_user = cursor.fetchone()
        
        if updated_user:
            print("\n📋 更新后的用户信息:")
            print(f"   ID: {updated_user[0]}")
            print(f"   用户名: {updated_user[1]}")
            print(f"   邮箱: {updated_user[2]}")
            print(f"   密码哈希: {updated_user[3][:50]}...")
            print(f"   激活状态: {updated_user[4]}")
            print(f"   超级用户: {updated_user[5]}")
            
            # 测试密码验证
            if AuthService.verify_password(new_password, updated_user[3]):
                print(f"\n🎉 密码验证成功！")
                print(f"📝 登录信息:")
                print(f"   用户名: admin")
                print(f"   密码: {new_password}")
                print(f"   邮箱: {updated_user[2]}")
            else:
                print(f"\n❌ 密码验证失败！")
                return False
        
        conn.close()
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保已安装所需依赖: pip install passlib[bcrypt]")
        return False
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 开始修复管理员登录问题...")
    print("=" * 50)
    
    if fix_admin_login():
        print("=" * 50)
        print("✅ 修复完成！现在可以使用以下信息登录:")
        print("   用户名: admin")
        print("   密码: admin123456")
    else:
        print("=" * 50)
        print("❌ 修复失败！")
        sys.exit(1)
