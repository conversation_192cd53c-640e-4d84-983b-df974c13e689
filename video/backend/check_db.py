#!/usr/bin/env python3
"""
检查数据库状态
"""

import sqlite3
import os

def check_database():
    """检查数据库状态"""
    db_path = './app.db'
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print("数据库中的表:")
        for table in tables:
            print(f"  - {table[0]}")
        
        # 检查用户表
        if any('users' in table for table in tables):
            cursor.execute("SELECT id, username, email, hashed_password, is_active, is_superuser FROM users")
            users = cursor.fetchall()
            print(f"\n用户表中有 {len(users)} 个用户:")
            for user in users:
                print(f"  ID: {user[0]}, 用户名: {user[1]}, 邮箱: {user[2]}")
                print(f"  密码哈希: {user[3][:50]}...")
                print(f"  激活状态: {user[4]}, 超级用户: {user[5]}")
                print()
        else:
            print("\n用户表不存在")
        
        conn.close()
        
    except Exception as e:
        print(f"检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_database()
