"""
认证相关API端点
"""

from datetime import timedelta
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.config import settings
from app.core.auth import AuthService
from app.core.dependencies import get_current_active_user, get_current_user
from app.services.user_service import UserService
from app.schemas.user import (
    UserCreate, User, UserLogin, Token, PasswordResetRequest, 
    PasswordReset, UserPasswordUpdate
)
from app.models.user import User as UserModel


router = APIRouter()


@router.post("/register", response_model=User, summary="用户注册")
async def register(
    user_create: UserCreate,
    db: Session = Depends(get_db)
):
    """
    用户注册
    
    - **username**: 用户名（3-50字符）
    - **email**: 邮箱地址
    - **password**: 密码（6-100字符）
    - **full_name**: 全名（可选）
    """
    try:
        user = UserService.create_user(db, user_create)
        return user
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="注册失败"
        )


@router.post("/login", response_model=Token, summary="用户登录")
async def login(
    user_login: UserLogin,
    db: Session = Depends(get_db)
):
    """
    用户登录
    
    - **username**: 用户名或邮箱
    - **password**: 密码
    - **remember_me**: 是否记住登录状态
    """
    user = UserService.authenticate_user(db, user_login.username, user_login.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 创建访问令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    if user_login.remember_me:
        access_token_expires = timedelta(days=30)  # 记住登录30天
    
    access_token = AuthService.create_access_token(
        data={"sub": str(user.id), "username": user.username},
        expires_delta=access_token_expires,
        remember_me=user_login.remember_me
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": int(access_token_expires.total_seconds()),
        "user": user
    }


@router.post("/login/oauth2", response_model=Token, summary="OAuth2 登录")
async def login_oauth2(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """
    OAuth2 兼容的登录端点
    """
    user = UserService.authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = AuthService.create_access_token(
        data={"sub": str(user.id), "username": user.username},
        expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": int(access_token_expires.total_seconds()),
        "user": user
    }


@router.post("/logout", summary="用户登出")
async def logout(
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    用户登出
    
    注意：由于使用JWT，实际的登出需要在客户端删除token
    这个端点主要用于记录登出事件或清理服务端会话
    """
    # TODO: 如果需要，可以在这里添加服务端会话清理逻辑
    # 例如：将token加入黑名单、清理用户会话记录等
    
    return {"message": "登出成功"}


@router.get("/me", response_model=User, summary="获取当前用户信息")
async def get_current_user_info(
    current_user: UserModel = Depends(get_current_active_user)
):
    """
    获取当前登录用户的详细信息
    """
    return current_user


@router.put("/me", response_model=User, summary="更新当前用户信息")
async def update_current_user(
    user_update: UserPasswordUpdate,
    current_user: UserModel = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    更新当前用户信息
    
    - **username**: 用户名
    - **email**: 邮箱地址
    - **full_name**: 全名
    - **avatar_url**: 头像URL
    """
    try:
        updated_user = UserService.update_user(db, current_user.id, user_update)
        return updated_user
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新用户信息失败"
        )


@router.put("/me/password", summary="修改密码")
async def change_password(
    password_update: UserPasswordUpdate,
    current_user: UserModel = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    修改当前用户密码
    
    - **current_password**: 当前密码
    - **new_password**: 新密码
    """
    try:
        UserService.update_password(
            db, 
            current_user.id, 
            password_update.current_password, 
            password_update.new_password
        )
        return {"message": "密码修改成功"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="密码修改失败"
        )


@router.post("/password-reset/request", summary="请求密码重置")
async def request_password_reset(
    request: PasswordResetRequest,
    db: Session = Depends(get_db)
):
    """
    请求密码重置
    
    - **email**: 邮箱地址
    """
    user = UserService.get_user_by_email(db, request.email)
    if not user:
        # 为了安全，即使用户不存在也返回成功消息
        return {"message": "如果邮箱存在，重置链接已发送"}
    
    # TODO: 实现邮件发送逻辑
    # 1. 生成重置令牌
    # 2. 保存到数据库
    # 3. 发送邮件
    
    return {"message": "如果邮箱存在，重置链接已发送"}


@router.post("/password-reset/confirm", summary="确认密码重置")
async def confirm_password_reset(
    reset_data: PasswordReset,
    db: Session = Depends(get_db)
):
    """
    确认密码重置
    
    - **token**: 重置令牌
    - **new_password**: 新密码
    """
    # 验证重置令牌
    user_id = AuthService.verify_password_reset_token(reset_data.token)
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效或已过期的重置令牌"
        )
    
    user = UserService.get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 更新密码
    user.hashed_password = AuthService.get_password_hash(reset_data.new_password)
    db.commit()
    
    return {"message": "密码重置成功"}


@router.get("/verify-token", summary="验证令牌")
async def verify_token(
    current_user: UserModel = Depends(get_current_user)
):
    """
    验证当前令牌是否有效
    """
    if current_user:
        return {"valid": True, "user": current_user}
    else:
        return {"valid": False}
