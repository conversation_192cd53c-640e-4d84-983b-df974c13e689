"""
角色管理API端点
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.dependencies import get_current_admin_user
from app.services.user_service import RoleService, PermissionService
from app.schemas.user import Role, RoleCreate, RoleUpdate, Permission
from app.models.user import User as UserModel


router = APIRouter()


@router.get("/", response_model=List[Role], summary="获取角色列表")
async def get_roles(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    current_user: UserModel = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    获取角色列表（需要管理员权限）
    """
    roles = RoleService.get_roles(db, skip=skip, limit=limit)
    return roles


@router.get("/{role_id}", response_model=Role, summary="获取角色详情")
async def get_role(
    role_id: int,
    current_user: UserModel = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    获取指定角色的详细信息
    """
    role = RoleService.get_role_by_id(db, role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在"
        )
    return role


@router.post("/", response_model=Role, summary="创建角色")
async def create_role(
    role_create: RoleCreate,
    current_user: UserModel = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    创建新角色（需要管理员权限）
    """
    try:
        role = RoleService.create_role(
            db, 
            name=role_create.name,
            description=role_create.description,
            is_default=role_create.is_default
        )
        return role
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建角色失败"
        )


@router.put("/{role_id}", response_model=Role, summary="更新角色")
async def update_role(
    role_id: int,
    role_update: RoleUpdate,
    current_user: UserModel = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    更新角色信息
    """
    role = RoleService.get_role_by_id(db, role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在"
        )
    
    try:
        # 更新角色信息
        update_data = role_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(role, field, value)
        
        db.commit()
        db.refresh(role)
        
        return role
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新角色失败"
        )


@router.delete("/{role_id}", summary="删除角色")
async def delete_role(
    role_id: int,
    current_user: UserModel = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    删除角色
    """
    role = RoleService.get_role_by_id(db, role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在"
        )
    
    # 检查是否有用户使用此角色
    if role.users:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无法删除正在使用的角色"
        )
    
    try:
        db.delete(role)
        db.commit()
        return {"message": "角色删除成功"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除角色失败"
        )


@router.get("/{role_id}/permissions", response_model=List[Permission], summary="获取角色权限")
async def get_role_permissions(
    role_id: int,
    current_user: UserModel = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    获取角色的权限列表
    """
    role = RoleService.get_role_by_id(db, role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在"
        )
    
    return role.permissions


@router.put("/{role_id}/permissions", summary="设置角色权限")
async def set_role_permissions(
    role_id: int,
    permission_ids: List[int],
    current_user: UserModel = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    设置角色的权限
    """
    role = RoleService.get_role_by_id(db, role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在"
        )
    
    try:
        # 清除现有权限
        role.permissions.clear()
        
        # 添加新权限
        for permission_id in permission_ids:
            permission = db.query(Permission).filter(Permission.id == permission_id).first()
            if permission:
                role.permissions.append(permission)
        
        db.commit()
        
        return {"message": "角色权限设置成功"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="设置角色权限失败"
        )
