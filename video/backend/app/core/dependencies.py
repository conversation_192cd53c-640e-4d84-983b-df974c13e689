"""
FastAPI 依赖项
"""

from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.auth import AuthService, PermissionChecker
from app.services.user_service import UserService
from app.models.user import User


# HTTP Bearer 认证方案
security = HTTPBearer(auto_error=False)


def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
) -> Optional[User]:
    """获取当前用户（可选）"""
    if not credentials:
        return None
    
    try:
        payload = AuthService.verify_token(credentials.credentials)
        user_id: int = payload.get("sub")
        if user_id is None:
            return None
    except HTTPException:
        return None
    
    user = UserService.get_user_by_id(db, user_id)
    if user is None or not user.is_active:
        return None
    
    return user


def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """获取当前活跃用户（必需）"""
    if current_user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未认证",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return current_user


def get_current_superuser(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """获取当前超级用户"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    return current_user


def get_current_admin_user(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """获取当前管理员用户"""
    if not (current_user.is_superuser or PermissionChecker.can_access_admin_panel(current_user)):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user


class RequirePermission:
    """权限检查依赖项"""
    
    def __init__(self, permission: str):
        self.permission = permission
    
    def __call__(
        self,
        current_user: User = Depends(get_current_active_user),
        db: Session = Depends(get_db)
    ) -> User:
        # 超级用户拥有所有权限
        if current_user.is_superuser:
            return current_user
        
        # 检查用户权限
        user_permissions = UserService.get_user_permissions(db, current_user.id)
        if not PermissionChecker.check_permission(user_permissions, self.permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"缺少权限: {self.permission}"
            )
        
        return current_user


class RequireResourcePermission:
    """资源权限检查依赖项"""
    
    def __init__(self, resource: str, action: str):
        self.resource = resource
        self.action = action
    
    def __call__(
        self,
        current_user: User = Depends(get_current_active_user),
        db: Session = Depends(get_db)
    ) -> User:
        # 超级用户拥有所有权限
        if current_user.is_superuser:
            return current_user
        
        # 检查用户权限
        user_permissions = UserService.get_user_permissions(db, current_user.id)
        if not PermissionChecker.check_resource_permission(
            user_permissions, self.resource, self.action
        ):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"缺少权限: {self.resource}:{self.action}"
            )
        
        return current_user


# 常用权限依赖项
require_user_read = RequireResourcePermission("user", "read")
require_user_create = RequireResourcePermission("user", "create")
require_user_update = RequireResourcePermission("user", "update")
require_user_delete = RequireResourcePermission("user", "delete")

require_task_read = RequireResourcePermission("task", "read")
require_task_create = RequireResourcePermission("task", "create")
require_task_update = RequireResourcePermission("task", "update")
require_task_delete = RequireResourcePermission("task", "delete")

require_video_read = RequireResourcePermission("video", "read")
require_video_create = RequireResourcePermission("video", "create")
require_video_update = RequireResourcePermission("video", "update")
require_video_delete = RequireResourcePermission("video", "delete")

require_clip_read = RequireResourcePermission("clip", "read")
require_clip_create = RequireResourcePermission("clip", "create")
require_clip_update = RequireResourcePermission("clip", "update")
require_clip_delete = RequireResourcePermission("clip", "delete")


def get_optional_user(
    current_user: Optional[User] = Depends(get_current_user)
) -> Optional[User]:
    """获取可选的当前用户（用于可选认证的端点）"""
    return current_user


class OwnershipChecker:
    """所有权检查器"""
    
    @staticmethod
    def check_task_ownership(task_id: int, user: User, db: Session) -> bool:
        """检查任务所有权"""
        # 超级用户和管理员可以访问所有任务
        if user.is_superuser or PermissionChecker.can_access_admin_panel(user):
            return True
        
        # TODO: 实现任务所有权检查逻辑
        # 这里需要根据实际业务逻辑来实现
        # 例如：检查任务是否属于当前用户
        return True
    
    @staticmethod
    def check_video_ownership(video_id: int, user: User, db: Session) -> bool:
        """检查视频所有权"""
        # 超级用户和管理员可以访问所有视频
        if user.is_superuser or PermissionChecker.can_access_admin_panel(user):
            return True
        
        # TODO: 实现视频所有权检查逻辑
        return True
    
    @staticmethod
    def check_clip_ownership(clip_id: int, user: User, db: Session) -> bool:
        """检查片段所有权"""
        # 超级用户和管理员可以访问所有片段
        if user.is_superuser or PermissionChecker.can_access_admin_panel(user):
            return True
        
        # TODO: 实现片段所有权检查逻辑
        return True


class RequireOwnership:
    """所有权检查依赖项"""
    
    def __init__(self, resource_type: str, id_param: str = "id"):
        self.resource_type = resource_type
        self.id_param = id_param
    
    def __call__(
        self,
        resource_id: int,
        current_user: User = Depends(get_current_active_user),
        db: Session = Depends(get_db)
    ) -> User:
        if self.resource_type == "task":
            if not OwnershipChecker.check_task_ownership(resource_id, current_user, db):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权访问此任务"
                )
        elif self.resource_type == "video":
            if not OwnershipChecker.check_video_ownership(resource_id, current_user, db):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权访问此视频"
                )
        elif self.resource_type == "clip":
            if not OwnershipChecker.check_clip_ownership(resource_id, current_user, db):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权访问此片段"
                )
        
        return current_user
