"""
认证相关核心功能
"""

from datetime import datetime, timedelta
from typing import Optional, Union
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status
from app.core.config import settings
import secrets
import string


# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class AuthService:
    """认证服务类"""
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def get_password_hash(password: str) -> str:
        """获取密码哈希"""
        return pwd_context.hash(password)
    
    @staticmethod
    def create_access_token(
        data: dict, 
        expires_delta: Optional[timedelta] = None,
        remember_me: bool = False
    ) -> str:
        """创建访问令牌"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            # 如果选择记住登录，延长过期时间到30天
            expire_minutes = 30 * 24 * 60 if remember_me else settings.ACCESS_TOKEN_EXPIRE_MINUTES
            expire = datetime.utcnow() + timedelta(minutes=expire_minutes)
        
        to_encode.update({"exp": expire})
        
        # 添加 JWT ID 用于会话管理
        jti = AuthService.generate_token()
        to_encode.update({"jti": jti})
        
        encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        return encoded_jwt
    
    @staticmethod
    def verify_token(token: str) -> dict:
        """验证令牌"""
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
            return payload
        except JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证令牌",
                headers={"WWW-Authenticate": "Bearer"},
            )
    
    @staticmethod
    def generate_token(length: int = 32) -> str:
        """生成随机令牌"""
        alphabet = string.ascii_letters + string.digits
        return ''.join(secrets.choice(alphabet) for _ in range(length))
    
    @staticmethod
    def generate_password_reset_token() -> str:
        """生成密码重置令牌"""
        return AuthService.generate_token(64)
    
    @staticmethod
    def create_password_reset_token(user_id: int) -> str:
        """创建密码重置令牌"""
        data = {
            "user_id": user_id,
            "type": "password_reset",
            "exp": datetime.utcnow() + timedelta(hours=1)  # 1小时过期
        }
        return jwt.encode(data, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    
    @staticmethod
    def verify_password_reset_token(token: str) -> Optional[int]:
        """验证密码重置令牌"""
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
            if payload.get("type") != "password_reset":
                return None
            return payload.get("user_id")
        except JWTError:
            return None


class PermissionChecker:
    """权限检查器"""
    
    @staticmethod
    def check_permission(user_permissions: list, required_permission: str) -> bool:
        """检查用户是否具有指定权限"""
        return required_permission in user_permissions
    
    @staticmethod
    def check_resource_permission(
        user_permissions: list, 
        resource: str, 
        action: str
    ) -> bool:
        """检查用户是否具有资源操作权限"""
        permission_name = f"{resource}:{action}"
        return PermissionChecker.check_permission(user_permissions, permission_name)
    
    @staticmethod
    def is_admin(user_roles: list) -> bool:
        """检查用户是否为管理员"""
        admin_roles = ["admin", "administrator", "super_admin"]
        return any(role.name.lower() in admin_roles for role in user_roles)
    
    @staticmethod
    def can_access_admin_panel(user) -> bool:
        """检查用户是否可以访问管理面板"""
        return user.is_superuser or PermissionChecker.is_admin(user.roles)


# 权限常量定义
class Permissions:
    """权限常量"""
    
    # 用户管理权限
    USER_CREATE = "user:create"
    USER_READ = "user:read"
    USER_UPDATE = "user:update"
    USER_DELETE = "user:delete"
    USER_LIST = "user:list"
    
    # 任务管理权限
    TASK_CREATE = "task:create"
    TASK_READ = "task:read"
    TASK_UPDATE = "task:update"
    TASK_DELETE = "task:delete"
    TASK_LIST = "task:list"
    
    # 视频管理权限
    VIDEO_CREATE = "video:create"
    VIDEO_READ = "video:read"
    VIDEO_UPDATE = "video:update"
    VIDEO_DELETE = "video:delete"
    VIDEO_LIST = "video:list"
    
    # 片段管理权限
    CLIP_CREATE = "clip:create"
    CLIP_READ = "clip:read"
    CLIP_UPDATE = "clip:update"
    CLIP_DELETE = "clip:delete"
    CLIP_LIST = "clip:list"
    
    # 角色管理权限
    ROLE_CREATE = "role:create"
    ROLE_READ = "role:read"
    ROLE_UPDATE = "role:update"
    ROLE_DELETE = "role:delete"
    ROLE_LIST = "role:list"
    
    # 系统管理权限
    SYSTEM_ADMIN = "system:admin"
    SYSTEM_CONFIG = "system:config"


# 默认角色和权限配置
DEFAULT_ROLES_PERMISSIONS = {
    "super_admin": {
        "description": "超级管理员",
        "permissions": [
            # 所有权限
            Permissions.USER_CREATE, Permissions.USER_READ, Permissions.USER_UPDATE, 
            Permissions.USER_DELETE, Permissions.USER_LIST,
            Permissions.TASK_CREATE, Permissions.TASK_READ, Permissions.TASK_UPDATE, 
            Permissions.TASK_DELETE, Permissions.TASK_LIST,
            Permissions.VIDEO_CREATE, Permissions.VIDEO_READ, Permissions.VIDEO_UPDATE, 
            Permissions.VIDEO_DELETE, Permissions.VIDEO_LIST,
            Permissions.CLIP_CREATE, Permissions.CLIP_READ, Permissions.CLIP_UPDATE, 
            Permissions.CLIP_DELETE, Permissions.CLIP_LIST,
            Permissions.ROLE_CREATE, Permissions.ROLE_READ, Permissions.ROLE_UPDATE, 
            Permissions.ROLE_DELETE, Permissions.ROLE_LIST,
            Permissions.SYSTEM_ADMIN, Permissions.SYSTEM_CONFIG
        ]
    },
    "admin": {
        "description": "管理员",
        "permissions": [
            Permissions.USER_READ, Permissions.USER_LIST,
            Permissions.TASK_CREATE, Permissions.TASK_READ, Permissions.TASK_UPDATE, 
            Permissions.TASK_DELETE, Permissions.TASK_LIST,
            Permissions.VIDEO_CREATE, Permissions.VIDEO_READ, Permissions.VIDEO_UPDATE, 
            Permissions.VIDEO_DELETE, Permissions.VIDEO_LIST,
            Permissions.CLIP_CREATE, Permissions.CLIP_READ, Permissions.CLIP_UPDATE, 
            Permissions.CLIP_DELETE, Permissions.CLIP_LIST,
        ]
    },
    "user": {
        "description": "普通用户",
        "permissions": [
            Permissions.TASK_CREATE, Permissions.TASK_READ, Permissions.TASK_UPDATE, 
            Permissions.TASK_LIST,
            Permissions.VIDEO_CREATE, Permissions.VIDEO_READ, Permissions.VIDEO_UPDATE, 
            Permissions.VIDEO_LIST,
            Permissions.CLIP_CREATE, Permissions.CLIP_READ, Permissions.CLIP_UPDATE, 
            Permissions.CLIP_LIST,
        ],
        "is_default": True
    },
    "guest": {
        "description": "访客",
        "permissions": [
            Permissions.TASK_READ, Permissions.TASK_LIST,
            Permissions.VIDEO_READ, Permissions.VIDEO_LIST,
            Permissions.CLIP_READ, Permissions.CLIP_LIST,
        ]
    }
}
