"""
用户认证相关的 Pydantic 模式
"""

from pydantic import BaseModel, EmailStr, Field, ConfigDict
from typing import List, Optional
from datetime import datetime


# 基础模式
class UserBase(BaseModel):
    """用户基础模式"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱地址")
    full_name: Optional[str] = Field(None, max_length=100, description="全名")


class UserCreate(UserBase):
    """用户创建模式"""
    password: str = Field(..., min_length=6, max_length=100, description="密码")


class UserUpdate(BaseModel):
    """用户更新模式"""
    username: Optional[str] = Field(None, min_length=3, max_length=50, description="用户名")
    email: Optional[EmailStr] = Field(None, description="邮箱地址")
    full_name: Optional[str] = Field(None, max_length=100, description="全名")
    avatar_url: Optional[str] = Field(None, max_length=500, description="头像URL")
    is_active: Optional[bool] = Field(None, description="是否激活")


class UserPasswordUpdate(BaseModel):
    """用户密码更新模式"""
    current_password: str = Field(..., description="当前密码")
    new_password: str = Field(..., min_length=6, max_length=100, description="新密码")


class UserInDB(UserBase):
    """数据库中的用户模式"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    avatar_url: Optional[str] = None
    is_active: bool = True
    is_superuser: bool = False
    last_login: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime


class User(UserInDB):
    """用户响应模式"""
    roles: List["Role"] = []


# 角色相关模式
class RoleBase(BaseModel):
    """角色基础模式"""
    name: str = Field(..., min_length=2, max_length=50, description="角色名称")
    description: Optional[str] = Field(None, description="角色描述")


class RoleCreate(RoleBase):
    """角色创建模式"""
    is_default: bool = Field(False, description="是否为默认角色")


class RoleUpdate(BaseModel):
    """角色更新模式"""
    name: Optional[str] = Field(None, min_length=2, max_length=50, description="角色名称")
    description: Optional[str] = Field(None, description="角色描述")
    is_default: Optional[bool] = Field(None, description="是否为默认角色")


class Role(RoleBase):
    """角色响应模式"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    is_default: bool = False
    created_at: datetime
    updated_at: datetime
    permissions: List["Permission"] = []


# 权限相关模式
class PermissionBase(BaseModel):
    """权限基础模式"""
    name: str = Field(..., min_length=2, max_length=100, description="权限名称")
    description: Optional[str] = Field(None, description="权限描述")
    resource: str = Field(..., min_length=2, max_length=50, description="资源类型")
    action: str = Field(..., min_length=2, max_length=50, description="操作类型")


class PermissionCreate(PermissionBase):
    """权限创建模式"""
    pass


class Permission(PermissionBase):
    """权限响应模式"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    created_at: datetime


# 认证相关模式
class UserLogin(BaseModel):
    """用户登录模式"""
    username: str = Field(..., description="用户名或邮箱")
    password: str = Field(..., description="密码")
    remember_me: bool = Field(False, description="记住登录状态")


class Token(BaseModel):
    """令牌响应模式"""
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: User


class TokenData(BaseModel):
    """令牌数据模式"""
    user_id: Optional[int] = None
    username: Optional[str] = None


class PasswordResetRequest(BaseModel):
    """密码重置请求模式"""
    email: EmailStr = Field(..., description="邮箱地址")


class PasswordReset(BaseModel):
    """密码重置模式"""
    token: str = Field(..., description="重置令牌")
    new_password: str = Field(..., min_length=6, max_length=100, description="新密码")


# 用户管理相关模式
class UserListResponse(BaseModel):
    """用户列表响应模式"""
    users: List[User]
    total: int
    page: int
    size: int


class UserAdminCreate(UserCreate):
    """管理员创建用户模式"""
    is_active: bool = Field(True, description="是否激活")
    is_superuser: bool = Field(False, description="是否为超级用户")
    role_ids: List[int] = Field([], description="角色ID列表")


class UserAdminUpdate(UserUpdate):
    """管理员更新用户模式"""
    is_superuser: Optional[bool] = Field(None, description="是否为超级用户")
    role_ids: Optional[List[int]] = Field(None, description="角色ID列表")


# 解决前向引用
User.model_rebuild()
Role.model_rebuild()
