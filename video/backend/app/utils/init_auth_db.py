#!/usr/bin/env python3
"""
独立的数据库初始化脚本
用于创建用户认证系统的数据库表和初始数据
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import hashlib
import secrets
from datetime import datetime

# 数据库配置
DATABASE_URL = "sqlite:///./app.db"
engine = create_engine(DATABASE_URL, echo=True)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def hash_password(password: str) -> str:
    """简单的密码哈希函数"""
    import bcrypt
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')


def create_tables():
    """创建数据库表"""
    print("创建数据库表...")
    
    with engine.connect() as conn:
        # 创建用户表
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(50) NOT NULL UNIQUE,
                email VARCHAR(100) NOT NULL UNIQUE,
                hashed_password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100),
                avatar_url VARCHAR(500),
                is_active BOOLEAN DEFAULT 1,
                is_superuser BOOLEAN DEFAULT 0,
                last_login DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """))
        
        # 创建角色表
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS roles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(50) NOT NULL UNIQUE,
                description TEXT,
                is_default BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """))
        
        # 创建权限表
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL UNIQUE,
                description TEXT,
                resource VARCHAR(50) NOT NULL,
                action VARCHAR(50) NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """))
        
        # 创建用户角色关联表
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS user_roles (
                user_id INTEGER NOT NULL,
                role_id INTEGER NOT NULL,
                PRIMARY KEY (user_id, role_id),
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (role_id) REFERENCES roles (id)
            )
        """))
        
        # 创建角色权限关联表
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS role_permissions (
                role_id INTEGER NOT NULL,
                permission_id INTEGER NOT NULL,
                PRIMARY KEY (role_id, permission_id),
                FOREIGN KEY (role_id) REFERENCES roles (id),
                FOREIGN KEY (permission_id) REFERENCES permissions (id)
            )
        """))
        
        # 创建密码重置表
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS password_resets (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                token VARCHAR(255) NOT NULL UNIQUE,
                expires_at DATETIME NOT NULL,
                used BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """))
        
        # 创建用户会话表
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS user_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                token_jti VARCHAR(255) NOT NULL UNIQUE,
                device_info TEXT,
                ip_address VARCHAR(45),
                expires_at DATETIME NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """))
        
        conn.commit()
        print("数据库表创建完成")


def create_permissions():
    """创建权限"""
    print("创建权限...")
    
    permissions = [
        # 用户管理权限
        ("user:create", "创建用户", "user", "create"),
        ("user:read", "查看用户", "user", "read"),
        ("user:update", "更新用户", "user", "update"),
        ("user:delete", "删除用户", "user", "delete"),
        ("user:list", "用户列表", "user", "list"),
        
        # 任务管理权限
        ("task:create", "创建任务", "task", "create"),
        ("task:read", "查看任务", "task", "read"),
        ("task:update", "更新任务", "task", "update"),
        ("task:delete", "删除任务", "task", "delete"),
        ("task:list", "任务列表", "task", "list"),
        
        # 视频管理权限
        ("video:create", "创建视频", "video", "create"),
        ("video:read", "查看视频", "video", "read"),
        ("video:update", "更新视频", "video", "update"),
        ("video:delete", "删除视频", "video", "delete"),
        ("video:list", "视频列表", "video", "list"),
        
        # 片段管理权限
        ("clip:create", "创建片段", "clip", "create"),
        ("clip:read", "查看片段", "clip", "read"),
        ("clip:update", "更新片段", "clip", "update"),
        ("clip:delete", "删除片段", "clip", "delete"),
        ("clip:list", "片段列表", "clip", "list"),
        
        # 角色管理权限
        ("role:create", "创建角色", "role", "create"),
        ("role:read", "查看角色", "role", "read"),
        ("role:update", "更新角色", "role", "update"),
        ("role:delete", "删除角色", "role", "delete"),
        ("role:list", "角色列表", "role", "list"),
        
        # 系统管理权限
        ("system:admin", "系统管理", "system", "admin"),
        ("system:config", "系统配置", "system", "config"),
    ]
    
    with engine.connect() as conn:
        for name, description, resource, action in permissions:
            conn.execute(text("""
                INSERT OR IGNORE INTO permissions (name, description, resource, action)
                VALUES (:name, :description, :resource, :action)
            """), {"name": name, "description": description, "resource": resource, "action": action})
        
        conn.commit()
        print("权限创建完成")


def create_roles():
    """创建角色"""
    print("创建角色...")
    
    with engine.connect() as conn:
        # 创建超级管理员角色
        conn.execute(text("""
            INSERT OR IGNORE INTO roles (name, description, is_default)
            VALUES ('super_admin', '超级管理员', 0)
        """))
        
        # 创建管理员角色
        conn.execute(text("""
            INSERT OR IGNORE INTO roles (name, description, is_default)
            VALUES ('admin', '管理员', 0)
        """))
        
        # 创建普通用户角色
        conn.execute(text("""
            INSERT OR IGNORE INTO roles (name, description, is_default)
            VALUES ('user', '普通用户', 1)
        """))
        
        # 创建访客角色
        conn.execute(text("""
            INSERT OR IGNORE INTO roles (name, description, is_default)
            VALUES ('guest', '访客', 0)
        """))
        
        conn.commit()
        print("角色创建完成")


def assign_permissions_to_roles():
    """为角色分配权限"""
    print("为角色分配权限...")
    
    with engine.connect() as conn:
        # 获取角色ID
        super_admin_id = conn.execute(text("SELECT id FROM roles WHERE name = 'super_admin'")).fetchone()[0]
        admin_id = conn.execute(text("SELECT id FROM roles WHERE name = 'admin'")).fetchone()[0]
        user_id = conn.execute(text("SELECT id FROM roles WHERE name = 'user'")).fetchone()[0]
        guest_id = conn.execute(text("SELECT id FROM roles WHERE name = 'guest'")).fetchone()[0]
        
        # 获取所有权限ID
        all_permissions = conn.execute(text("SELECT id, name FROM permissions")).fetchall()
        permission_map = {name: id for id, name in all_permissions}
        
        # 超级管理员拥有所有权限
        for permission_id, _ in all_permissions:
            conn.execute(text("""
                INSERT OR IGNORE INTO role_permissions (role_id, permission_id)
                VALUES (:role_id, :permission_id)
            """), {"role_id": super_admin_id, "permission_id": permission_id})
        
        # 管理员权限
        admin_permissions = [
            "user:read", "user:list",
            "task:create", "task:read", "task:update", "task:delete", "task:list",
            "video:create", "video:read", "video:update", "video:delete", "video:list",
            "clip:create", "clip:read", "clip:update", "clip:delete", "clip:list",
        ]
        for perm_name in admin_permissions:
            if perm_name in permission_map:
                conn.execute(text("""
                    INSERT OR IGNORE INTO role_permissions (role_id, permission_id)
                    VALUES (:role_id, :permission_id)
                """), {"role_id": admin_id, "permission_id": permission_map[perm_name]})
        
        # 普通用户权限
        user_permissions = [
            "task:create", "task:read", "task:update", "task:list",
            "video:create", "video:read", "video:update", "video:list",
            "clip:create", "clip:read", "clip:update", "clip:list",
        ]
        for perm_name in user_permissions:
            if perm_name in permission_map:
                conn.execute(text("""
                    INSERT OR IGNORE INTO role_permissions (role_id, permission_id)
                    VALUES (:role_id, :permission_id)
                """), {"role_id": user_id, "permission_id": permission_map[perm_name]})
        
        # 访客权限
        guest_permissions = [
            "task:read", "task:list",
            "video:read", "video:list",
            "clip:read", "clip:list",
        ]
        for perm_name in guest_permissions:
            if perm_name in permission_map:
                conn.execute(text("""
                    INSERT OR IGNORE INTO role_permissions (role_id, permission_id)
                    VALUES (:role_id, :permission_id)
                """), {"role_id": guest_id, "permission_id": permission_map[perm_name]})
        
        conn.commit()
        print("权限分配完成")


def create_admin_user():
    """创建管理员用户"""
    print("创建管理员用户...")
    
    admin_password = os.getenv("ADMIN_PASSWORD", "admin123456")
    
    try:
        import bcrypt
        hashed_password = bcrypt.hashpw(admin_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    except ImportError:
        # 如果没有 bcrypt，使用简单的哈希
        import hashlib
        hashed_password = hashlib.sha256(admin_password.encode()).hexdigest()
    
    with engine.connect() as conn:
        # 创建管理员用户
        conn.execute(text("""
            INSERT OR IGNORE INTO users (username, email, full_name, hashed_password, is_active, is_superuser)
            VALUES ('admin', '<EMAIL>', '系统管理员', :password, 1, 1)
        """), {"password": hashed_password})
        
        # 获取用户ID和角色ID
        admin_user = conn.execute(text("SELECT id FROM users WHERE username = 'admin'")).fetchone()
        super_admin_role = conn.execute(text("SELECT id FROM roles WHERE name = 'super_admin'")).fetchone()
        
        if admin_user and super_admin_role:
            # 分配超级管理员角色
            conn.execute(text("""
                INSERT OR IGNORE INTO user_roles (user_id, role_id)
                VALUES (:user_id, :role_id)
            """), {"user_id": admin_user[0], "role_id": super_admin_role[0]})
        
        conn.commit()
        print(f"管理员用户创建完成: admin (密码: {admin_password})")
        print("请在生产环境中修改默认管理员密码！")


def main():
    """主函数"""
    print("开始初始化用户认证数据库...")
    
    try:
        create_tables()
        create_permissions()
        create_roles()
        assign_permissions_to_roles()
        create_admin_user()
        
        print("用户认证数据库初始化完成！")
        
    except Exception as e:
        print(f"初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
