"""
Subtitle processing service for video analysis
"""

import os
import json
import time
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from loguru import logger

from app.models.task import Video, AudioTrack, Subtitle


class SubtitleService:
    """Service for subtitle processing and management"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def upload_manual_subtitle(self, video_id: int, srt_file_path: str, language: str = "zh-cn") -> Subtitle:
        """Upload and process manual SRT subtitle file"""
        video = self.db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise ValueError(f"Video {video_id} not found")
        
        if not os.path.exists(srt_file_path):
            raise FileNotFoundError(f"SRT file not found: {srt_file_path}")
        
        try:
            # Parse SRT content
            subtitle_content = self._parse_srt_file(srt_file_path)
            
            # Create subtitle record
            subtitle = Subtitle(
                video_id=video_id,
                subtitle_type="manual",
                language=language,
                file_path=srt_file_path,
                content=json.dumps(subtitle_content),
                confidence=1.0  # Manual subtitles have 100% confidence
            )
            
            self.db.add(subtitle)
            self.db.commit()
            
            logger.info(f"Successfully uploaded manual subtitle for video {video_id}")
            return subtitle
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to upload manual subtitle for video {video_id}: {e}")
            raise
    
    def generate_automatic_subtitle(self, video_id: int, audio_track_id: Optional[int] = None) -> Subtitle:
        """Generate automatic subtitle from audio content"""
        video = self.db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise ValueError(f"Video {video_id} not found")
        
        # Get audio track to process
        if audio_track_id:
            audio_track = self.db.query(AudioTrack).filter(
                AudioTrack.id == audio_track_id,
                AudioTrack.video_id == video_id
            ).first()
        else:
            # Use first audio track if not specified
            audio_track = self.db.query(AudioTrack).filter(
                AudioTrack.video_id == video_id
            ).first()
        
        if not audio_track:
            raise ValueError(f"No audio track found for video {video_id}")
        
        if not audio_track.file_path or not os.path.exists(audio_track.file_path):
            raise FileNotFoundError(f"Audio file not found: {audio_track.file_path}. Please extract audio first.")
        
        try:
            start_time = time.time()
            
            # Use the existing audio parsing functionality
            subtitle_content = self._generate_subtitle_from_audio(audio_track.file_path)
            
            processing_time = time.time() - start_time
            
            # Calculate average confidence
            avg_confidence = self._calculate_average_confidence(subtitle_content)
            
            # Create subtitle record
            subtitle = Subtitle(
                video_id=video_id,
                audio_track_id=audio_track_id,
                subtitle_type="auto_generated",
                language="zh-cn",  # Default to Chinese
                content=json.dumps(subtitle_content),
                confidence=avg_confidence,
                processing_time=processing_time
            )
            
            self.db.add(subtitle)
            self.db.commit()
            
            logger.info(f"Successfully generated automatic subtitle for video {video_id}")
            return subtitle
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to generate automatic subtitle for video {video_id}: {e}")
            raise
    
    def _parse_srt_file(self, srt_file_path: str) -> List[Dict]:
        """Parse SRT file into structured format"""
        subtitle_entries = []
        
        with open(srt_file_path, 'r', encoding='utf-8') as file:
            content = file.read().strip()
        
        # Split by double newlines to get individual subtitle blocks
        blocks = content.split('\n\n')
        
        for block in blocks:
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                try:
                    # Parse subtitle number
                    subtitle_num = int(lines[0])
                    
                    # Parse time range
                    time_line = lines[1]
                    start_time, end_time = time_line.split(' --> ')
                    
                    # Parse text (may be multiple lines)
                    text = '\n'.join(lines[2:])
                    
                    subtitle_entries.append({
                        "index": subtitle_num,
                        "start_time": self._parse_srt_time(start_time),
                        "end_time": self._parse_srt_time(end_time),
                        "text": text,
                        "confidence": 1.0
                    })
                    
                except (ValueError, IndexError) as e:
                    logger.warning(f"Failed to parse subtitle block: {block[:50]}... Error: {e}")
                    continue
        
        return subtitle_entries
    
    def _parse_srt_time(self, time_str: str) -> float:
        """Parse SRT time format (HH:MM:SS,mmm) to seconds"""
        try:
            time_part, ms_part = time_str.split(',')
            hours, minutes, seconds = map(int, time_part.split(':'))
            milliseconds = int(ms_part)
            
            total_seconds = hours * 3600 + minutes * 60 + seconds + milliseconds / 1000.0
            return total_seconds
        except (ValueError, IndexError):
            return 0.0
    
    def _generate_subtitle_from_audio(self, audio_file_path: str) -> List[Dict]:
        """
        Generate subtitle from audio using the audio processing service
        """
        try:
            import requests
            import os

            # Check if audio file exists
            if not os.path.exists(audio_file_path):
                raise FileNotFoundError(f"Audio file not found: {audio_file_path}")

            # Call the audio processing service
            audio_service_url = os.getenv('AUDIO_SERVICE_URL', 'http://localhost:8001')

            # Prepare request data
            request_data = {
                "input": audio_file_path
            }

            # Make request to audio service
            response = requests.post(
                f"{audio_service_url}/audio",
                json=request_data,
                timeout=300  # 5 minutes timeout for audio processing
            )

            if response.status_code != 200:
                raise Exception(f"Audio service returned status {response.status_code}: {response.text}")

            result = response.json()

            # Convert result to subtitle format
            subtitle_entries = []

            # Handle the response from audio service
            if 'output' in result:
                output = result['output']

                if isinstance(output, dict) and 'sentences' in output:
                    # Handle sentence-level results with timestamps
                    for i, sentence in enumerate(output['sentences']):
                        subtitle_entries.append({
                            "index": i + 1,
                            "start_time": sentence.get('start', 0.0),
                            "end_time": sentence.get('end', 0.0),
                            "text": sentence.get('text', ''),
                            "confidence": sentence.get('confidence', 0.8)
                        })
                elif isinstance(output, dict) and 'text' in output:
                    # Handle simple text result without timestamps
                    subtitle_entries.append({
                        "index": 1,
                        "start_time": 0.0,
                        "end_time": 0.0,
                        "text": output['text'],
                        "confidence": output.get('confidence', 0.8)
                    })
                elif isinstance(output, str):
                    # Handle plain text result
                    subtitle_entries.append({
                        "index": 1,
                        "start_time": 0.0,
                        "end_time": 0.0,
                        "text": output,
                        "confidence": 0.8
                    })
                elif isinstance(output, list):
                    # Handle list of results (common format)
                    for i, item in enumerate(output):
                        if isinstance(item, dict):
                            subtitle_entries.append({
                                "index": i + 1,
                                "start_time": item.get('start', item.get('start_time', 0.0)),
                                "end_time": item.get('end', item.get('end_time', 0.0)),
                                "text": item.get('text', ''),
                                "confidence": item.get('confidence', 0.8)
                            })
                        elif isinstance(item, str):
                            subtitle_entries.append({
                                "index": i + 1,
                                "start_time": 0.0,
                                "end_time": 0.0,
                                "text": item,
                                "confidence": 0.8
                            })

            if not subtitle_entries:
                logger.warning("No subtitle content generated from audio")
                # Return empty list instead of mock data
                return []

            return subtitle_entries

        except requests.exceptions.ConnectionError:
            logger.error("Cannot connect to audio processing service")
            raise Exception("Audio processing service is not available")
        except requests.exceptions.Timeout:
            logger.error("Audio processing service timeout")
            raise Exception("Audio processing timeout")
        except Exception as e:
            logger.error(f"Failed to generate subtitle from audio: {e}")
            raise Exception(f"Subtitle generation failed: {str(e)}")
    

    
    def _calculate_average_confidence(self, subtitle_content: List[Dict]) -> float:
        """Calculate average confidence score from subtitle entries"""
        if not subtitle_content:
            return 0.0
        
        total_confidence = sum(entry.get('confidence', 0.0) for entry in subtitle_content)
        return total_confidence / len(subtitle_content)
    
    def export_subtitle_to_srt(self, subtitle_id: int, output_path: str) -> str:
        """Export subtitle to SRT format"""
        subtitle = self.db.query(Subtitle).filter(Subtitle.id == subtitle_id).first()
        if not subtitle:
            raise ValueError(f"Subtitle {subtitle_id} not found")
        
        try:
            subtitle_content = json.loads(subtitle.content)
            srt_content = self._convert_to_srt_format(subtitle_content)
            
            with open(output_path, 'w', encoding='utf-8') as file:
                file.write(srt_content)
            
            logger.info(f"Exported subtitle {subtitle_id} to {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Failed to export subtitle {subtitle_id}: {e}")
            raise
    
    def _convert_to_srt_format(self, subtitle_content: List[Dict]) -> str:
        """Convert subtitle content to SRT format"""
        srt_lines = []
        
        for entry in subtitle_content:
            index = entry.get('index', 1)
            start_time = self._seconds_to_srt_time(entry.get('start_time', 0.0))
            end_time = self._seconds_to_srt_time(entry.get('end_time', 0.0))
            text = entry.get('text', '')
            
            srt_lines.append(f"{index}")
            srt_lines.append(f"{start_time} --> {end_time}")
            srt_lines.append(text)
            srt_lines.append("")  # Empty line between entries
        
        return '\n'.join(srt_lines)
    
    def _seconds_to_srt_time(self, seconds: float) -> str:
        """Convert seconds to SRT time format (HH:MM:SS,mmm)"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"
