"""
用户管理服务
"""

from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from fastapi import HTTPEx<PERSON>, status
from datetime import datetime

from app.models.user import User, Role, Permission, PasswordReset, UserSession
from app.schemas.user import UserCreate, UserUpdate, UserAdminCreate, UserAdminUpdate
from app.core.auth import AuthService


class UserService:
    """用户服务类"""
    
    @staticmethod
    def get_user_by_id(db: Session, user_id: int) -> Optional[User]:
        """根据ID获取用户"""
        return db.query(User).filter(User.id == user_id).first()
    
    @staticmethod
    def get_user_by_username(db: Session, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        return db.query(User).filter(User.username == username).first()
    
    @staticmethod
    def get_user_by_email(db: Session, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        return db.query(User).filter(User.email == email).first()
    
    @staticmethod
    def get_user_by_username_or_email(db: Session, username_or_email: str) -> Optional[User]:
        """根据用户名或邮箱获取用户"""
        return db.query(User).filter(
            or_(User.username == username_or_email, User.email == username_or_email)
        ).first()
    
    @staticmethod
    def create_user(db: Session, user_create: UserCreate) -> User:
        """创建用户"""
        # 检查用户名是否已存在
        if UserService.get_user_by_username(db, user_create.username):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )
        
        # 检查邮箱是否已存在
        if UserService.get_user_by_email(db, user_create.email):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在"
            )
        
        # 创建用户
        hashed_password = AuthService.get_password_hash(user_create.password)
        db_user = User(
            username=user_create.username,
            email=user_create.email,
            full_name=user_create.full_name,
            hashed_password=hashed_password
        )
        
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        
        # 分配默认角色
        default_role = RoleService.get_default_role(db)
        if default_role:
            db_user.roles.append(default_role)
            db.commit()
            db.refresh(db_user)
        
        return db_user
    
    @staticmethod
    def update_user(db: Session, user_id: int, user_update: UserUpdate) -> User:
        """更新用户信息"""
        db_user = UserService.get_user_by_id(db, user_id)
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 检查用户名是否已被其他用户使用
        if user_update.username and user_update.username != db_user.username:
            existing_user = UserService.get_user_by_username(db, user_update.username)
            if existing_user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="用户名已存在"
                )
        
        # 检查邮箱是否已被其他用户使用
        if user_update.email and user_update.email != db_user.email:
            existing_user = UserService.get_user_by_email(db, user_update.email)
            if existing_user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="邮箱已存在"
                )
        
        # 更新用户信息
        update_data = user_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_user, field, value)
        
        db.commit()
        db.refresh(db_user)
        return db_user
    
    @staticmethod
    def update_password(db: Session, user_id: int, current_password: str, new_password: str) -> bool:
        """更新用户密码"""
        db_user = UserService.get_user_by_id(db, user_id)
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 验证当前密码
        if not AuthService.verify_password(current_password, db_user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="当前密码错误"
            )
        
        # 更新密码
        db_user.hashed_password = AuthService.get_password_hash(new_password)
        db.commit()
        return True
    
    @staticmethod
    def authenticate_user(db: Session, username_or_email: str, password: str) -> Optional[User]:
        """验证用户登录"""
        user = UserService.get_user_by_username_or_email(db, username_or_email)
        if not user:
            return None
        if not user.is_active:
            return None
        if not AuthService.verify_password(password, user.hashed_password):
            return None
        
        # 更新最后登录时间
        user.last_login = datetime.utcnow()
        db.commit()
        
        return user
    
    @staticmethod
    def get_users(
        db: Session, 
        skip: int = 0, 
        limit: int = 100,
        search: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> tuple[List[User], int]:
        """获取用户列表"""
        query = db.query(User)
        
        # 搜索过滤
        if search:
            query = query.filter(
                or_(
                    User.username.contains(search),
                    User.email.contains(search),
                    User.full_name.contains(search)
                )
            )
        
        # 状态过滤
        if is_active is not None:
            query = query.filter(User.is_active == is_active)
        
        total = query.count()
        users = query.offset(skip).limit(limit).all()
        
        return users, total
    
    @staticmethod
    def delete_user(db: Session, user_id: int) -> bool:
        """删除用户"""
        db_user = UserService.get_user_by_id(db, user_id)
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        db.delete(db_user)
        db.commit()
        return True
    
    @staticmethod
    def get_user_permissions(db: Session, user_id: int) -> List[str]:
        """获取用户权限列表"""
        user = UserService.get_user_by_id(db, user_id)
        if not user:
            return []
        
        permissions = set()
        for role in user.roles:
            for permission in role.permissions:
                permissions.add(permission.name)
        
        return list(permissions)


class RoleService:
    """角色服务类"""
    
    @staticmethod
    def get_role_by_id(db: Session, role_id: int) -> Optional[Role]:
        """根据ID获取角色"""
        return db.query(Role).filter(Role.id == role_id).first()
    
    @staticmethod
    def get_role_by_name(db: Session, name: str) -> Optional[Role]:
        """根据名称获取角色"""
        return db.query(Role).filter(Role.name == name).first()
    
    @staticmethod
    def get_default_role(db: Session) -> Optional[Role]:
        """获取默认角色"""
        return db.query(Role).filter(Role.is_default == True).first()
    
    @staticmethod
    def get_roles(db: Session, skip: int = 0, limit: int = 100) -> List[Role]:
        """获取角色列表"""
        return db.query(Role).offset(skip).limit(limit).all()
    
    @staticmethod
    def create_role(db: Session, name: str, description: str = None, is_default: bool = False) -> Role:
        """创建角色"""
        if RoleService.get_role_by_name(db, name):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="角色名称已存在"
            )
        
        db_role = Role(
            name=name,
            description=description,
            is_default=is_default
        )
        
        db.add(db_role)
        db.commit()
        db.refresh(db_role)
        return db_role


class PermissionService:
    """权限服务类"""
    
    @staticmethod
    def get_permission_by_name(db: Session, name: str) -> Optional[Permission]:
        """根据名称获取权限"""
        return db.query(Permission).filter(Permission.name == name).first()
    
    @staticmethod
    def create_permission(
        db: Session, 
        name: str, 
        description: str, 
        resource: str, 
        action: str
    ) -> Permission:
        """创建权限"""
        if PermissionService.get_permission_by_name(db, name):
            return PermissionService.get_permission_by_name(db, name)
        
        db_permission = Permission(
            name=name,
            description=description,
            resource=resource,
            action=action
        )
        
        db.add(db_permission)
        db.commit()
        db.refresh(db_permission)
        return db_permission
