#!/usr/bin/env python3
"""
重置管理员用户脚本
"""

import sqlite3
import bcrypt
import os
import sys

def reset_admin_user():
    """重置管理员用户"""
    db_path = './app.db'
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查用户表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        if not cursor.fetchone():
            print("用户表不存在，需要先初始化数据库")
            return False
        
        # 查看现有用户
        cursor.execute("SELECT id, username, email, hashed_password FROM users WHERE username='admin'")
        admin_user = cursor.fetchone()
        
        if admin_user:
            print(f"找到管理员用户: ID={admin_user[0]}, 用户名={admin_user[1]}, 邮箱={admin_user[2]}")
            print(f"当前密码哈希: {admin_user[3][:50]}...")
        else:
            print("未找到管理员用户")
        
        # 重置密码
        new_password = "admin123456"
        print(f"重置密码为: {new_password}")
        
        # 使用bcrypt加密密码
        hashed_password = bcrypt.hashpw(new_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        print(f"新密码哈希: {hashed_password[:50]}...")
        
        if admin_user:
            # 更新现有用户
            cursor.execute("""
                UPDATE users 
                SET hashed_password = ?, is_active = 1, is_superuser = 1
                WHERE username = 'admin'
            """, (hashed_password,))
            print("更新现有管理员用户")
        else:
            # 创建新用户
            cursor.execute("""
                INSERT INTO users (username, email, full_name, hashed_password, is_active, is_superuser)
                VALUES ('admin', '<EMAIL>', '系统管理员', ?, 1, 1)
            """, (hashed_password,))
            print("创建新管理员用户")
        
        conn.commit()
        
        # 验证更新结果
        cursor.execute("SELECT id, username, email, hashed_password, is_active, is_superuser FROM users WHERE username='admin'")
        updated_user = cursor.fetchone()
        
        if updated_user:
            print("\n更新后的用户信息:")
            print(f"  ID: {updated_user[0]}")
            print(f"  用户名: {updated_user[1]}")
            print(f"  邮箱: {updated_user[2]}")
            print(f"  密码哈希: {updated_user[3][:50]}...")
            print(f"  激活状态: {updated_user[4]}")
            print(f"  超级用户: {updated_user[5]}")
            
            # 测试密码验证
            if bcrypt.checkpw(new_password.encode('utf-8'), updated_user[3].encode('utf-8')):
                print(f"\n✅ 密码验证成功！可以使用用户名 'admin' 和密码 '{new_password}' 登录")
            else:
                print(f"\n❌ 密码验证失败！")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"重置失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始重置管理员用户...")
    if reset_admin_user():
        print("重置完成！")
    else:
        print("重置失败！")
        sys.exit(1)
